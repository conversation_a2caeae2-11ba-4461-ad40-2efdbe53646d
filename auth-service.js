// auth-service.js
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { app } = require('electron');
const fetch = require('node-fetch');

class AuthService {
    constructor() {
        this.authDir = path.join(app.getPath('userData'), 'auth');
        this.authFile = path.join(this.authDir, 'user-auth.json');
        this.secretKey = this.getOrCreateSecretKey();
        
        // Ensure auth directory exists
        if (!fs.existsSync(this.authDir)) {
            fs.mkdirSync(this.authDir, { recursive: true });
        }
    }

    getOrCreateSecretKey() {
        const keyFile = path.join(app.getPath('userData'), 'app.key');
        
        if (fs.existsSync(keyFile)) {
            return fs.readFileSync(keyFile, 'utf8');
        } else {
            const key = crypto.randomBytes(32).toString('hex');
            fs.writeFileSync(keyFile, key, 'utf8');
            return key;
        }
    }

    encrypt(text) {
        const algorithm = 'aes-256-cbc';
        const key = Buffer.from(this.secretKey, 'hex');
        const iv = crypto.randomBytes(16);
        
        const cipher = crypto.createCipher(algorithm, key);
        let encrypted = cipher.update(text, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        
        return iv.toString('hex') + ':' + encrypted;
    }

    decrypt(encryptedText) {
        try {
            const algorithm = 'aes-256-cbc';
            const key = Buffer.from(this.secretKey, 'hex');
            const textParts = encryptedText.split(':');
            const iv = Buffer.from(textParts.shift(), 'hex');
            const encrypted = textParts.join(':');
            
            const decipher = crypto.createDecipher(algorithm, key);
            let decrypted = decipher.update(encrypted, 'hex', 'utf8');
            decrypted += decipher.final('utf8');
            
            return decrypted;
        } catch (error) {
            console.error('Decryption failed:', error);
            return null;
        }
    }

    async saveAuth(authData) {
        try {
            const dataToSave = {
                user: authData.user,
                credentials: authData.credentials ? {
                    username: authData.credentials.username,
                    password: this.encrypt(authData.credentials.password)
                } : null,
                savedAt: new Date().toISOString()
            };

            fs.writeFileSync(this.authFile, JSON.stringify(dataToSave, null, 2));
            console.log('✅ Authentication data saved');
            return true;
        } catch (error) {
            console.error('❌ Failed to save auth data:', error);
            return false;
        }
    }

    async getSavedAuth() {
        try {
            if (!fs.existsSync(this.authFile)) {
                return null;
            }

            const data = JSON.parse(fs.readFileSync(this.authFile, 'utf8'));
            
            // Decrypt password if it exists
            if (data.credentials && data.credentials.password) {
                const decryptedPassword = this.decrypt(data.credentials.password);
                if (decryptedPassword) {
                    data.credentials.password = decryptedPassword;
                } else {
                    // If decryption fails, remove credentials
                    data.credentials = null;
                }
            }

            return data;
        } catch (error) {
            console.error('❌ Failed to load auth data:', error);
            return null;
        }
    }

    async clearAuth() {
        try {
            if (fs.existsSync(this.authFile)) {
                fs.unlinkSync(this.authFile);
                console.log('✅ Authentication data cleared');
            }
            return true;
        } catch (error) {
            console.error('❌ Failed to clear auth data:', error);
            return false;
        }
    }

    async autoLogin() {
        try {
            const savedAuth = await this.getSavedAuth();
            if (!savedAuth || !savedAuth.credentials) {
                return null;
            }

            console.log('🔄 Attempting auto-login...');
            console.log('Auto-login credentials:', { email: savedAuth.credentials.username });

            const response = await fetch('https://easyvoice.kambaaincorporation.in/apiv2/auth/user-login', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    email: savedAuth.credentials.username,
                    password: savedAuth.credentials.password
                })
            });

            console.log('Auto-login response status:', response.status);
            const data = await response.json();
            console.log('Auto-login response data:', data);

            if (response.ok && data.success) {
                // Check if user account is active
                if (!data.isActive) {
                    console.log('❌ Auto-login failed: User subscription has expired');
                    await this.clearAuth();
                    return null;
                }

                // Create user object with API response data
                const updatedAuth = {
                    user: {
                        userId: data.userId,
                        name: data.name,
                        email: data.email,
                        phone: data.phone,
                        city: data.city,
                        state: data.state,
                        plan: data.plan,
                        isActive: data.isActive,
                        isAdmin: data.isAdmin,
                        tokensUsed: data.tokensUsed,
                        cost: data.cost,
                        weeklyTokens: data.weeklyTokens,
                        weeklyLimit: data.weeklyLimit,
                        createdAt: data.createdAt,
                        source: data.source,
                        username: data.email // For compatibility
                    },
                    credentials: savedAuth.credentials
                };

                await this.saveAuth(updatedAuth);
                console.log('✅ Auto-login successful');
                console.log(`📊 User Plan: ${data.plan} | Tokens Used: ${data.tokensUsed} | Weekly Limit: ${data.weeklyLimit}`);
                return updatedAuth;
            } else {
                console.log('❌ Auto-login failed, clearing saved credentials');
                await this.clearAuth();
                return null;
            }
        } catch (error) {
            console.error('❌ Auto-login error:', error);

            // Clear stored credentials when there's an API error
            await this.clearAuth();

            // Throw the error so main.js can handle it properly
            throw new Error(`Auto-login API failed: ${error.message}`);
        }
    }

    async login(email, password) {
        try {
            console.log('🔄 Attempting login...');
            console.log('Login credentials:', { email });

            const response = await fetch('https://easyvoice.kambaaincorporation.in/apiv2/auth/user-login', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    email: email,
                    password: password
                })
            });

            console.log('Login response status:', response.status);
            const data = await response.json();
            console.log('Login response data:', data);

            if (response.ok && data.success) {
                // Check if user account is active
                if (!data.isActive) {
                    return {
                        success: false,
                        message: 'Your subscription has expired. Please recharge to continue using the service.',
                        isExpired: true
                    };
                }

                // Create user object with all the API response data
                const user = {
                    userId: data.userId,
                    name: data.name,
                    email: data.email,
                    phone: data.phone,
                    city: data.city,
                    state: data.state,
                    plan: data.plan,
                    isActive: data.isActive,
                    isAdmin: data.isAdmin,
                    tokensUsed: data.tokensUsed,
                    cost: data.cost,
                    weeklyTokens: data.weeklyTokens,
                    weeklyLimit: data.weeklyLimit,
                    createdAt: data.createdAt,
                    source: data.source,
                    username: data.email // For compatibility
                };

                console.log('✅ Login successful');
                console.log(`📊 User Plan: ${data.plan} | Tokens Used: ${data.tokensUsed} | Weekly Limit: ${data.weeklyLimit}`);

                return {
                    success: true,
                    user: user,
                    message: data.message
                };
            } else {
                return {
                    success: false,
                    message: data.message || 'Login failed'
                };
            }
        } catch (error) {
            console.error('❌ Login error:', error);
            return {
                success: false,
                message: 'Network error. Please check your connection and try again.'
            };
        }
    }

    async verifyUser(userData) {
        try {
            // Simple verification - check if user data is valid and active
            if (!userData || !userData.userId || !userData.email) {
                return false;
            }

            // Check if account is still active
            return userData.isActive === true;
        } catch (error) {
            console.error('❌ User verification error:', error);
            return false;
        }
    }
}

module.exports = AuthService;
